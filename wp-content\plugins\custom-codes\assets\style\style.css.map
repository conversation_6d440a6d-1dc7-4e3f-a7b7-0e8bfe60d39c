{"version": 3, "sources": ["style.scss", "style.css"], "names": [], "mappings": "AAcA;EACC,oBAAoB;EACpB,2BAAsB;OAAtB,sBAAsB;EACtB,mCAA8B;EAA9B,8BAA8B;ACb/B;;ADgBA;EACC,qBAAqB;ACbtB;;ADgBA;EACC,gBAAgB;EAChB,qBAAqB;ACbtB;;ADgBA;EACC,gBAAgB;EAChB,uBAAkB;OAAlB,kBAAkB;EAwgBlB,eAAA;ACphBD;;ADUA;EAME,WAAW;EACX,YAFoB;EAGpB,wBAAmB;OAAnB,mBAAmB;EACnB,kBAAkB;EAClB,eAAe;ACZjB;;ADEA;EAaG,kBAAkB;EAClB,MAAM;EACN,OAAO;EACP,WAAW;EACX,YAAY;EACZ,0CAA0C;EAC1C,2BAAsB;OAAtB,sBAAsB;EACtB,sBAAiB;EAAjB,iBAAiB;ACXpB;;ADTA;EAwBG,kBAAkB;EAClB,UAAU;EACV,SAAS;EACT,gCAA2B;OAA3B,2BAA2B;EAC3B,MAAM;EACN,wBAAgB;KAAhB,qBAAgB;UAAhB,gBAAgB;EAChB,eAAe;EACf,gBAAgB;EAChB,WAAW;EACX,YAAY;EACZ,uBAAkB;OAAlB,kBAAkB;EAClB,YAAY;EACZ,cAAc;EACd,SAAS;EACT,sBAAiB;EAAjB,iBAAiB;EACjB,aAAa;EACb,qBAAgB;OAAhB,gBAAgB;ACXnB;;AD7BA;EA2CI,SAAK;ACVT;;ADjCA;EA8CK,yBAAkC;ACTvC;;ADrCA;EAmDI,aAAa;ACVjB;;ADzCA;EA0DE,yBAAyB;EACtB,WAAW;EACd,kBAAkB;EAClB,kBAAkB;ACbpB;;ADhDA;EAgEG,mBAAmB;EAChB,iBAAiB;ACZvB;;ADrDA;EAuEE,qBAAqB;EACrB,YAAY;ACdd;;AD1DA;EA4EG,cAnGc;ACqFjB;;AD9DA;EAiFE,iBAAiB;EACjB,gBAAgB;EAChB,qBAAgB;OAAhB,gBAAgB;ACflB;;ADpEA;EAuFE,iBAAa;EAAb,aAAa;EACb,sBAA8B;OAA9B,8BAA8B;EAC9B,sBAAmB;OAAnB,mBAAmB;EACnB,YA7GkB;EA8GlB,yBAvHe;EAwHf,+BAA0B;OAA1B,0BAA0B;EAC1B,2BAAsB;OAAtB,sBAAsB;EACtB,eAAe;EACf,kDAvHsC;EAwHtC,kBAAkB;EAClB,UAAU;ACfZ;;ADlFA;EAoGG,aAAa;EACb,sBAAsB;EACtB,sBAAmB;OAAnB,mBAAmB;EACnB,SAAS;ACdZ;;ADzFA;EA2GG,cAAc;ACdjB;;AD7FA;EAkHI,aAAa;EACb,qBAAgB;OAAhB,gBAAgB;ACjBpB;;ADlGA;EAuHM,aA9IW;AC6HjB;;ADtGA;EAgIG,wBAAgB;KAAhB,qBAAgB;UAAhB,gBAAgB;EAChB,uQAAuQ;EACvQ,+BAA0B;OAA1B,0BAA0B;EAC1B,4BAA4B;EAC5B,4BAA4B;EAC5B,yBAhKa;EAiKb,yBAjKa;EAkKb,uBAAkB;OAAlB,kBAAkB;EAClB,YAAY;EACZ,eAAe;EACf,gBAAgB;EAChB,sBAAsB;EACtB,eAAe;ACtBlB;;ADtHA;EAiJI,sCAAsC;ACvB1C;;AD1HA;EAwJI,YAAY;EACZ,eAAe;AC1BnB;;AD/HA;EA4JK,aArLU;AC4Jf;;ADnIA;EAmKG,iBAAa;EAAb,aAAa;AC5BhB;;ADvIA;EAsKI,oBAAoB;EACpB,uBAAkB;EAAlB,kBAAkB;EAClB,gBAAgB;AC3BpB;;AD7IA;EA4KI,YAAY;EACZ,yBAxMY;EAyMZ,cAxMU;EAyMV,YAAY;EACZ,eAAe;EACf,SAAS;EACT,eAAe;EACf,aAAa;EACb,eAAe;EACf,gBAAgB;EAChB,iBAAa;EAAb,aAAa;EACb,sBAAmB;OAAnB,mBAAmB;AC3BvB;;AD5JA;EA0LK,+BAA2B;OAA3B,2BAA2B;EAC3B,kCAA8B;OAA9B,8BAA8B;AC1BnC;;ADjKA;EA+LK,gCAA4B;OAA5B,4BAA4B;EAC5B,mCAA+B;OAA/B,+BAA+B;AC1BpC;;ADtKA;EAoMK,kBAAkB;AC1BvB;;AD1KA;EAuMM,gBAAgB;EAChB,kBAAkB;EAClB,UAAU;EACV,SAAS;EACT,eAAe;EACf,aAAa;EACb,iBAAiB;ACzBvB;;ADpLA;EAmNK,2CA3OmC;ACgNxC;;ADxLA;EAwNK,sBAAsB;EACtB,cArPY;ACyNjB;;AD7LA;EA4NM,kCAA6B;OAA7B,6BAA6B;AC3BnC;;ADjMA;EAiOK,iBAAiB;AC5BtB;;ADrMA;EAoOM,8BAAyB;OAAzB,yBAAyB;AC3B/B;;ADzMA;EA4OM,aAAa;AC/BnB;;ADiCM;EA9ON;IA+OO,eAAe;EC7BpB;AACF;;ADnNA;EAsPK,YAAY;AC/BjB;;ADvNA;EA0PK,aAAa;AC/BlB;;AD3NA;EAmQE,yBA/Re;EAgSf,kBAAkB;EAClB,UAAU;ACpCZ;;ADjOA;EAwQG,kBAAkB;EAClB,UAAU;EACV,OAAO;EACP,WAAW;EACX,uBAAuB;EACvB,iBAAa;EAAb,aAAa;ACnChB;;AD1OA;EAgRI,MAAM;EACN,qBAAuB;OAAvB,uBAAuB;AClC3B;;AD/OA;EAqRI,SAAS;EACT,mBAAqB;OAArB,qBAAqB;AClCzB;;ADpPA;EA0RI,gBAAgB;EAChB,YAAY;AClChB;;ADzPA;EAgSG,kBAAkB;EAClB,gCAA2B;EAA3B,2BAA2B;ACnC9B;;AD9PA;EAoSI,YAAY;AClChB;;ADlQA;EAuSK,sCAAsC;EACtC,YAAY;EACZ,kBAAkB;EAClB,YAAY;EACZ,WAAW;ACjChB;;AD1QA;EAgTI,YAAY;AClChB;;AD9QA;EAoTI,WAAW;EACX,qBAAgB;OAAhB,gBAAgB;AClCpB;;ADnRA;EA2TG,mBAAmB;EACnB,+BAA2B;OAA3B,2BAA2B;EAC3B,gCAA4B;OAA5B,4BAA4B;EAC5B,YAAY;ACpCf;;AD1RA;EAiUI,YAAY;ACnChB;;AD9RA;EAqUI,wCAAmC;EAAnC,mCAAmC;ACnCvC;;ADlSA;EAyUI,gBAAgB;ACnCpB;;ADtSA;EA6UI,yBAzWa;ACsUjB;;AD1SA;EAgVK,yBA5WY;EA6WZ,iDAzWmC;ACuUxC;;AD/SA;EA6VK,2BAA2B;EAC3B,8BAA8B;AC1CnC;;ADpTA;EAsWK,uBAAuB;AC9C5B;;ADxTA;EA4WM,2BAA2B;EAC3B,8BAA8B;AChDpC;;AD7TA;EAyXE,YAAY;ACxDd;;ADjUA;EA6XE,iBAAa;EAAb,aAAa;EACb,sBAA8B;OAA9B,8BAA8B;EAC9B,sBAAmB;OAAnB,mBAAmB;EACnB,YAlZqB;EAmZrB,yBA5Zc;EA6Zd,+BAA+B;EAC/B,+CA3ZsC;EA4ZtC,+BAA0B;OAA1B,0BAA0B;EAC1B,2BAAsB;OAAtB,sBAAsB;ACxDxB;;AD7UA;EAwYG,+BAA+B;EAC/B,eAAe;EACf,gBAAgB;ACvDnB;;ADnVA;EA8YG,aAAa;EACb,sBAAsB;EACtB,SAAS;EACT,sBAAmB;OAAnB,mBAAmB;ACvDtB;;AD1VA;EAoZI,kBAAkB;ACtDtB;;AD9VA;EAwZI,kBAAyB;OAAzB,yBAAyB;ACtD7B;;ADlWA;EA2ZK,+BAA+B;EAC/B,mBAAmB;ACrDxB;;ADvWA;EAkaG,eAAe;EACf,iBAAa;EAAb,aAAa;EACb,sBAAmB;OAAnB,mBAAmB;ACvDtB;;AD7WA;EAwaG,yBAnca;EAocb,YAAY;EACZ,aAAa;EACb,qBAAgB;OAAhB,gBAAgB;EAChB,qBAAgB;OAAhB,gBAAgB;EAChB,+BAA0B;OAA1B,0BAA0B;ACvD7B;;ADtXA;EAgbI,eAAe;EACf,yBA7ca;ACuZjB;;AD3XA;EAqbI,YAAY;ACtDhB;;AD/XA;EA0bG,yBArda;EAsdb,YAAY;EACZ,gDApdqC;EAqdrC,YAAY;EACZ,eAAe;EACf,YAAY;EACZ,qBAAgB;OAAhB,gBAAgB;EAChB,mCAA+B;OAA/B,+BAA+B;EAC/B,eAAe;EACf,aAAa;ACvDhB;;AD5YA;EAucI,2CA/doC;ACwaxC;;ADhZA;EA2cI,sBAAsB;EACtB,cAveY;ACgbhB;;ADrZA;EAgdI,sBAAsB;EACtB,cA5eY;EA6eZ,iBAAiB;ACvDrB;;AD3ZA;EAqdK,UAAU;EACV,UAAU;EACV,oBAAe;OAAf,eAAe;ACtDpB;;ADjaA;EA4dK,YAAY;ACvDjB;;ADraA;EAgeK,YAAY;ACvDjB;;ADzaA;EAyeE,eAAe;EACf,cAAc;EACd,MAAM;EACN,OAAO;EACP,QAAQ;EACR,SAAS;EACT,WAAW;EACX,YAAY;EACZ,SAAS;EACT,yBAAyB;AC5D3B;;ADtbA;EAqfG,qBAAgB;OAAhB,gBAAgB;AC3DnB;;AD1bA;EA2fI,sCAA8D;EAA9D,iCAA8D;AC7DlE;;AD9bA;EA8fK,qBAAgB;OAAhB,gBAAgB;AC5DrB;;ADlcA;EAqgBG,qBAAgB;OAAhB,gBAAgB;AC/DnB;;ADtcA;EA4gBE,uCAAkC;OAAlC,kCAAkC;AClEpC;;ADoEC;EAAkB;IAAO,8BAAwB;SAAxB,yBAAwB;EC/DhD;AACF;;AD8DC;EAAkB;IAAO,8BAAwB;SAAxB,yBAAwB;EC/DhD;AACF;;ADiEA;EAGE,iBAAa;EAAb,aAAa;EACb,sBAAmB;OAAnB,mBAAmB;EACnB,gBAAgB;AChElB;;AD2DA;EAQG,mBAAmB;AC/DtB;;ADuDA;EAWI,YAAY;AC9DhB;;ADmDA;EAgBG,eAAe;EACf,WAAW;EACX,YAAY;EACZ,iBAAiB;AC/DpB;;AD4CA;EAuBG,YAAY;EACZ,YAAY;AC/Df;;ADuCA;EA8BE,gBAAgB;ACjElB;;ADmCA;EAkCE,gBAAgB;ACjElB;;ADwEC;EACC,YAAY;ACrEd;;AD2EA,aAAA;ACxEA;ED0EC,kBAAkB;ACxEnB;;AAEA;EDyEE,YAAY;ACvEd;;AAEA;;ED0EE,2BAA2B;EAC3B,oBAAoB;EACpB,kBAAkB;EAClB,gBAAgB;EAChB,qBAAqB;EACrB,gBAAgB;EAChB,YAAY;EACZ,4BAAuB;EAAvB,uBAAuB;EACvB,uBAAkB;OAAlB,kBAAkB;EAClB,sBAAsB;EACtB,cAAc;EACd,gDAAwC;OAAxC,2CAAwC;EACxC,uBAAuB;EACvB,wHAAwH;EACxH,eAAe;EACf,iBAAiB;EACjB,gBAAgB;EAChB,sBAAsB;EACtB,mBAAmB;EACnB,kBAAkB;EAClB,UAAU;EACV,sBAAiB;EAAjB,iBAAiB;EACjB,wBAAmB;OAAnB,mBAAmB;EAEnB,WAAW;EACX,WAAW;EACX,SAAS;EACT,gCAA2B;OAA3B,2BAA2B;ACxE7B;;AAEA;ED2EE,mBAAmB;EACnB,gBAAgB;EAChB,iBAAiB;EACjB,gBAAgB;EAChB,uBAAkB;OAAlB,kBAAkB;ACzEpB;;AAEA;ED0EG,UAAU;ACxEb;;AAEA;EDyEI,wCAAqC;EACrC,kBAAkB;ACvEtB;;AAEA;EDyEI,aAAa;EACb,qBAAqB;ACvEzB;;AAEA;ED0EG,eAAe;EACf,mBAAmB;EACnB,kBAAkB;EAClB,qBAAqB;ACxExB;;AAEA;;;;EDgFE,UAAU;EACV,6BAAwB;EAAxB,wBAAwB;AC3E1B;;AAEA;;;EDgFE,4BAAuB;OAAvB,uBAAuB;AC5EzB;;AAEA;;ED+EE,mBAAmB;EACnB,WAAW;AC5Eb;;AAEA;;EDgFG,yBAAyB;EACzB,WAAW;AC7Ed;;AAEA;;EDkFG,YAAY;EACZ,QAAQ;AC/EX;;AAEA;;;EDoFG,0BAAqB;EAArB,qBAAqB;AChFxB;;AAEA;;EDqFG,UAAU;EACV,UAAU;EACV,QAAQ;EACR,gCAA2B;OAA3B,2BAA2B;AClF9B;;AAEA;;EDsFI,QAAQ;EACR,oBAAe;OAAf,eAAe;ACnFnB;;AAEA;;;EDyFG,4BAAuB;EAAvB,uBAAuB;ACrF1B;;AAEA;;ED0FG,WAAW;EACX,SAAS;EACT,QAAQ;EACR,gCAA2B;OAA3B,2BAA2B;ACvF9B;;AAEA;;;ED4FG,2BAAsB;EAAtB,sBAAsB;ACxFzB", "file": "style.css", "sourcesContent": ["// Colors\n$darkest: #131619;\n$darker: #2F3235;\n$dark: #939393;\n$orange: orange;\n$border-color: rgba(255, 255, 255, 0.15);\n$wp-link: #0073aa;\n$wp-bg: #f1f1f1;\n\n// Dimensions\n$topbar-height: 58px;\n$bottombar-height: 36px;\n\n\n#wpbody-content {\n\tpadding-bottom: 45px;\n\tbox-sizing: border-box;\n\tmin-height: calc(100vh - 32px);\n}\n\n.loaded {\n\topacity: 1 !important;\n}\n\n.codes-pro-link {\n\tmargin-left: 5px;\n\tdisplay: inline-block;\n}\n\n#codes_editor_area {\n\tmargin-top: 10px;\n\tborder-radius: 4px;\n\n\t.switch-vertical {\n\t\t$switch-height: 25px;\n\t\twidth: 15px;\n\t\theight: $switch-height;\n\t\tborder-radius: 30px;\n\t\tposition: relative;\n\t\tcursor: pointer;\n\n\t\t.switch-fill {\n\t\t\tposition: absolute;\n\t\t\ttop: 0;\n\t\t\tleft: 0;\n\t\t\twidth: 100%;\n\t\t\theight: 100%;\n\t\t\tbackground-color: rgba(255, 255, 255, 0.5);\n\t\t\tborder-radius: inherit;\n\t\t\ttransition: 300ms;\n\t\t}\n\n\t\t& > input {\n\t\t\tposition: absolute;\n\t\t\tz-index: 1;\n\t\t\tleft: 50%;\n\t\t\ttransform: translateX(-50%);\n\t\t\ttop: 0;\n\t\t\tappearance: none;\n\t\t\tmin-width: auto;\n\t\t\tmin-height: auto;\n\t\t\twidth: 15px;\n\t\t\theight: 15px;\n\t\t\tborder-radius: 50%;\n\t\t\tborder: none;\n\t\t\tdisplay: block;\n\t\t\tmargin: 0;\n\t\t\ttransition: 300ms;\n\t\t\toutline: none;\n\t\t\tbox-shadow: none;\n\n\t\t\t&:checked {\n\t\t\t\ttop: #{$switch-height - 15px};\n\n\t\t\t\t& + .switch-fill {\n\t\t\t\t\tbackground-color: rgb(26, 206, 26);\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t&::before {\n\t\t\t\tcontent: none;\n\t\t\t}\n\t\t}\n\n\t}\n\n\t.lang-notice {\n\t\tbackground-color: #81848a;\n    \tcolor: #FFF;\n\t\tpadding: 11px 20px;\n\t\tposition: relative;\n\n\t\tsvg {\n\t\t\tvertical-align: sub;\n    \t\tmargin-right: 4px;\n\t\t}\n\n\t}\n\n\ta {\n\t\ttext-decoration: none;\n\t\tcolor: white;\n\n\t\t&:hover,\n\t\t&:active {\n\t\t\tcolor: $wp-link;\n\t\t}\n\t}\n\n\tselect {\n\t\tline-height: 35px;\n\t\tmin-height: 35px;\n\t\tbox-shadow: none;\n\t}\n\n\t#topbar {\n\t\tdisplay: flex;\n\t\tjustify-content: space-between;\n\t\talign-items: center;\n\t\theight: $topbar-height;\n\t\tbackground-color: $darkest;\n\t\tborder-radius: 4px 4px 0 0;\n\t\tbox-sizing: border-box;\n\t\tpadding: 0 10px;\n\t\tborder-bottom: 1px solid $border-color;\n\t\tposition: relative;\n\t\tz-index: 1;\n\n\t\t& > * {\n\t\t\tdisplay: grid;\n\t\t\tgrid-auto-flow: column;\n\t\t\talign-items: center;\n\t\t\tgap: 15px;\n\t\t}\n\n\t\timg, svg {\n\t\t\tdisplay: block;\n\t\t}\n\n\t\ta {\n\n\t\t\t&:hover,\n\t\t\t&:focus {\n\t\t\t\toutline: none;\n\t\t\t\tbox-shadow: none;\n\n\t\t\t\t& > svg {\n\t\t\t\t\tpath {\n\t\t\t\t\t\tfill: $wp-link;\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t}\n\n\t\tselect {\n\t\t\tappearance: none;\n\t\t\tbackground-image: url(data:image/svg+xml;charset=US-ASCII,%3Csvg%20width%3D%2220%22%20height%3D%2220%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3Cpath%20d%3D%22M5%206l5%205%205-5%202%201-7%207-7-7%202-1z%22%20fill%3D%22%23FFF%22%2F%3E%3C%2Fsvg%3E);\n\t\t\tbackground-size: 14px 14px;\n\t\t\tbackground-repeat: no-repeat;\n\t\t\tbackground-position: 98% 50%;\n\t\t\tbackground-color: $darker;\n\t\t\tborder: 1px solid $darker;\n\t\t\tborder-radius: 3px;\n\t\t\tcolor: white;\n\t\t\tfont-size: 13px;\n\t\t\tfont-weight: 500;\n\t\t\tpadding: 0 30px 0 10px;\n\t\t\tcursor: pointer;\n\n\n\t\t\t&:hover,\n\t\t\t&:focus {\n\t\t\t\tborder-color: rgba(255, 255, 255, 0.5);\n\t\t\t}\n\t\t}\n\n\t\t.indicators {\n\n\t\t\t& > .indicator {\n\t\t\t\tcolor: white;\n\t\t\t\tfont-size: 32px;\n\n\t\t\t\t&.unsaved {\n\t\t\t\t\tcolor: $orange;\n\t\t\t\t}\n\t\t\t}\n\n\t\t}\n\n\t\t.subtabs {\n\t\t\tdisplay: flex;\n\n\t\t\t.tooltip {\n\t\t\t\twhite-space: initial;\n\t\t\t\twidth: max-content;\n\t\t\t\tmax-width: 300px;\n\t\t\t}\n\n\t\t\tbutton {\n\t\t\t\tborder: none;\n\t\t\t\tbackground-color: $darker;\n\t\t\t\tcolor: $dark;\n\t\t\t\theight: 35px;\n\t\t\t\tpadding: 0 15px;\n\t\t\t\tmargin: 0;\n\t\t\t\tcursor: pointer;\n\t\t\t\toutline: none;\n\t\t\t\tfont-size: 12px;\n\t\t\t\tfont-weight: 500;\n\t\t\t\tdisplay: flex;\n\t\t\t\talign-items: center;\n\n\t\t\t\t&:first-child {\n\t\t\t\t\tborder-top-left-radius: 2px;\n\t\t\t\t\tborder-bottom-left-radius: 2px;\n\t\t\t\t}\n\n\t\t\t\t&:last-child {\n\t\t\t\t\tborder-top-right-radius: 2px;\n\t\t\t\t\tborder-bottom-right-radius: 2px;\n\t\t\t\t}\n\n\t\t\t\t&:not(.saved) {\n\t\t\t\t\tposition: relative;\n\n\t\t\t\t\t&::before {\n\t\t\t\t\t\tcontent: \"\\2022\";\n\t\t\t\t\t\tposition: absolute;\n\t\t\t\t\t\tright: 5px;\n\t\t\t\t\t\ttop: 10px;\n\t\t\t\t\t\tfont-size: 20px;\n\t\t\t\t\t\tcolor: orange;\n\t\t\t\t\t\tline-height: 10px;\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t&:hover,\n\t\t\t\t&:focus {\n\t\t\t\t\tbackground-color: $border-color;\n\t\t\t\t\t//color: $darker;\n\t\t\t\t}\n\n\t\t\t\t&.active {\n\t\t\t\t\tbackground-color: #fff;\n\t\t\t\t\tcolor: $darkest;\n\n\t\t\t\t\t&:focus {\n\t\t\t\t\t\tbox-shadow: 0 0 0 1px #007cba;\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t& > span.dashicons {\n\t\t\t\t\tmargin-right: 3px;\n\n\t\t\t\t\t&.l {\n\t\t\t\t\t\ttransform: rotate(-90deg);\n\t\t\t\t\t}\n\n\t\t\t\t}\n\n\t\t\t\t& > span.label {\n\n\t\t\t\t\t&.has-icon {\n\t\t\t\t\t\tdisplay: none;\n\n\t\t\t\t\t\t@media (min-width: 1330px) {\n\t\t\t\t\t\t\tdisplay: inline;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t}\n\n\t\t\t\t&.disabled {\n\t\t\t\t\topacity: 0.3;\n\t\t\t\t}\n\n\t\t\t\t&.hidden {\n\t\t\t\t\tdisplay: none;\n\t\t\t\t}\n\t\t\t}\n\n\t\t}\n\n\t}\n\n\t.editors {\n\t\tbackground-color: $darkest;\n\t\tposition: relative;\n\t\tz-index: 0;\n\n\t\t& > .editor-addition {\n\t\t\tposition: absolute;\n\t\t\tz-index: 5;\n\t\t\tleft: 0;\n\t\t\twidth: 100%;\n\t\t\theight: 25px !important;\n\t\t\tdisplay: flex;\n\n\t\t\t&.before {\n\t\t\t\ttop: 0;\n\t\t\t\talign-items: flex-start;\n\t\t\t}\n\n\t\t\t&.after {\n\t\t\t\tbottom: 0;\n\t\t\t\talign-items: flex-end;\n\t\t\t}\n\n\t\t\t& > span {\n\t\t\t\tpadding: 2px 5px;\n\t\t\t\topacity: 0.4;\n\t\t\t}\n\t\t}\n\n\t\t& > .editor {\n\t\t\tposition: relative;\n\t\t\theight: calc(100vh - 320px);\n\n\t\t\t&:not([writable]) {\n\t\t\t\topacity: 0.7;\n\n\t\t\t\t&::after {\n\t\t\t\t\tcontent: \"This editor is not writable\";\n\t\t\t\t\tcolor: white;\n\t\t\t\t\tposition: absolute;\n\t\t\t\t\tbottom: 10px;\n\t\t\t\t\tright: 10px;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t&.output {\n\t\t\t\topacity: 0.8;\n\t\t\t}\n\n\t\t\ttextarea {\n\t\t\t\twidth: 100%;\n\t\t\t\tborder-radius: 0;\n\t\t\t}\n\n\t\t}\n\n\t\t.CodeMirror {\n\t\t\tline-height: normal;\n\t\t\tborder-top-left-radius: 2px;\n\t\t\tborder-top-right-radius: 2px;\n\t\t\theight: 100%;\n\n\t\t\t&-placeholder {\n\t\t\t\topacity: 0.2;\n\t\t\t}\n\n\t\t\t&-lines > div {\n\t\t\t\tpadding-bottom: calc(100vh - 320px);\n\t\t\t}\n\n\t\t\t&-sizer {\n\t\t\t\tmargin-top: 10px;\n\t\t\t}\n\n\t\t\t&.cm-s-dark {\n\t\t\t\tbackground-color: $darkest;\n\n\t\t\t\t.CodeMirror-gutters {\n\t\t\t\t\tbackground-color: $darkest;\n\t\t\t\t\tborder-right: 1px solid $border-color;\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t}\n\n\t\t&.spacing {\n\n\t\t\t.CodeMirror {\n\n\t\t\t\t&-sizer {\n\t\t\t\t\tmargin-top: 25px !important;\n\t\t\t\t\tmargin-bottom: 25px !important;\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t\t&.larger {\n\n\t\t\t\t.editor-addition.before {\n\t\t\t\t\theight: 35px !important;\n\t\t\t\t}\n\n\t\t\t\t.CodeMirror {\n\n\t\t\t\t\t&-sizer {\n\t\t\t\t\t\tmargin-top: 35px !important;\n\t\t\t\t\t\tmargin-bottom: 25px !important;\n\t\t\t\t\t}\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t}\n\n\t}\n\n\t&.loading .editors {\n\t\topacity: 0.5;\n\t}\n\n\t#bottombar {\n\t\tdisplay: flex;\n\t\tjustify-content: space-between;\n\t\talign-items: center;\n\t\theight: $bottombar-height;\n\t\tbackground-color: $darker;\n\t\tcolor: rgba(255, 255, 255, 0.6);\n\t\tborder-top: 1px solid $border-color;\n\t\tborder-radius: 0 0 4px 4px;\n\t\tbox-sizing: border-box;\n\n\t\t* {\n\t\t\tcolor: rgba(255, 255, 255, 0.4);\n\t\t\tfont-size: 11px;\n\t\t\tfont-weight: 600;\n\t\t}\n\n\t\t& > * {\n\t\t\tdisplay: grid;\n\t\t\tgrid-auto-flow: column;\n\t\t\tgap: 10px;\n\t\t\talign-items: center;\n\n\t\t\t&.left {\n\t\t\t\tpadding-left: 10px;\n\t\t\t}\n\n\t\t\t&.right {\n\t\t\t\tjustify-content: flex-end;\n\n\t\t\t\tspan {\n\t\t\t\t\tcolor: rgba(255, 255, 255, 0.6);\n\t\t\t\t\tpadding-right: 10px;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\tlabel {\n\t\t\tcursor: default;\n\t\t\tdisplay: flex;\n\t\t\talign-items: center;\n\t\t}\n\n\t\tselect {\n\t\t\tbackground-color: $darker;\n\t\t\tborder: none;\n\t\t\toutline: none;\n\t\t\tbox-shadow: none;\n\t\t\tborder-radius: 0;\n\t\t\tbackground-size: 10px 10px;\n\n\t\t\t&:hover {\n\t\t\t\tcursor: pointer;\n\t\t\t\tbackground-color: $darkest;\n\t\t\t}\n\n\t\t\t&:focus {\n\t\t\t\tcolor: white;\n\t\t\t}\n\t\t}\n\n\t\tbutton {\n\t\t\tbackground-color: $darker;\n\t\t\tborder: none;\n\t\t\tborder-left: 1px solid $border-color;\n\t\t\theight: 35px;\n\t\t\tpadding: 0 20px;\n\t\t\tcolor: white;\n\t\t\tborder-radius: 0;\n\t\t\tborder-bottom-right-radius: 2px;\n\t\t\tcursor: pointer;\n\t\t\toutline: none;\n\n\t\t\t&:hover,\n\t\t\t&:focus {\n\t\t\t\tbackground-color: $border-color;\n\t\t\t}\n\n\t\t\t&.active {\n\t\t\t\tbackground-color: #fff;\n\t\t\t\tcolor: $darker;\n\t\t\t}\n\n\t\t\t&.save {\n\t\t\t\tbackground-color: #fff;\n\t\t\t\tcolor: $darker;\n\t\t\t\tmargin-left: -9px;\n\n\t\t\t\t&::after {\n\t\t\t\t\tright: 2px;\n\t\t\t\t\tleft: auto;\n\t\t\t\t\ttransform: none;\n\t\t\t\t}\n\n\t\t\t\t&:hover,\n\t\t\t\t&:focus {\n\t\t\t\t\topacity: 0.7;\n\t\t\t\t}\n\n\t\t\t\t&[disabled] {\n\t\t\t\t\topacity: 0.3;\n\t\t\t\t}\n\t\t\t}\n\n\t\t}\n\n\t}\n\n\t&.fullscreen {\n\t\tposition: fixed;\n\t\tz-index: 99999;\n\t\ttop: 0;\n\t\tleft: 0;\n\t\tright: 0;\n\t\tbottom: 0;\n\t\twidth: 100%;\n\t\theight: 100%;\n\t\tmargin: 0;\n\t\tbackground-color: #36393b;\n\n\t\t#topbar {\n\t\t\tborder-radius: 0;\n\t\t}\n\n\t\t.editors {\n\n\t\t\t& > .editor {\n\t\t\t\theight: calc(100vh - #{$topbar-height} - #{$bottombar-height});\n\n\t\t\t\t.CodeMirror {\n\t\t\t\t\tborder-radius: 0;\n\t\t\t\t}\n\t\t\t}\n\n\t\t}\n\n\t\t#bottombar {\n\t\t\tborder-radius: 0;\n\t\t}\n\n\t}\n\n\t/* Animations */\n\t.spin {\n\t\tanimation: spin 1s linear infinite;\n\t}\n\t@keyframes spin { 100% { transform:rotate(360deg); } }\n}\n\n#codes_location {\n\n\t& > label {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tmargin-top: 10px;\n\n\t\tinput {\n\t\t\tmargin-bottom: -3px;\n\n\t\t\t&[disabled] {\n\t\t\t\tcursor: auto;\n\t\t\t}\n\t\t}\n\n\t\tsvg, span.dashicons {\n\t\t\tfont-size: 20px;\n\t\t\twidth: 20px;\n\t\t\theight: 20px;\n\t\t\tmargin-right: 3px;\n\t\t}\n\n\t\t&[disabled] {\n\t\t\topacity: 0.7;\n\t\t\tcursor: auto;\n\t\t}\n\n\t}\n\n\tp {\n\t\tmargin-bottom: 0;\n\t}\n\n\thr {\n\t\tmargin-top: 15px;\n\t}\n\n}\n\n#codes_includes_box {\n\n\t.codes-free & {\n\t\topacity: 0.7;\n\t}\n\n}\n\n\n/* TOOLTIPS */\n[data-tooltip] {\n\tposition: relative;\n\n\t&.tooltip {\n\t\tcursor: help;\n\t}\n\n\t&:not(.tooltip-sub)::after,\n\t& > .tooltip {\n\t\tcontent: attr(data-tooltip);\n\t\tpointer-events: none;\n\t\tposition: absolute;\n\t\tz-index: 9999999;\n\t\tdisplay: inline-table;\n\t\tpadding: 4px 6px;\n\t\twidth: 120px;\n\t\tmin-height: fit-content;\n\t\tborder-radius: 4px;\n\t\tbackground-color: #FFF;\n\t\tcolor:\t#111820;\n\t\tbox-shadow: 0px 2px 15px rgba(0,0,0,0.5);\n\t\ttext-transform: initial;\n\t\tfont-family: -apple-system,BlinkMacSystemFont,\"Segoe UI\",Roboto,Oxygen-Sans,Ubuntu,Cantarell,\"Helvetica Neue\",sans-serif;\n\t\tfont-size: 12px;\n\t\tline-height: 15px;\n\t\tfont-weight: 500;\n\t\tletter-spacing: normal;\n\t\twhite-space: normal;\n\t\ttext-align: center;\n\t\topacity: 0;\n\t\ttransition: 500ms;\n\t\ttransition-delay: 0;\n\n\t\tbottom: 90%;\n\t\tright: auto;\n\t\tleft: 50%;\n\t\ttransform: translateX(-50%);\n\n\t}\n\n\t& > .tooltip {\n\t\tfont-weight: normal;\n\t\ttext-align: left;\n\t\tline-height: 20px;\n\t\toverflow: hidden;\n\t\tborder-radius: 4px;\n\n\t\t&.shortcuts {\n\t\t\tpadding: 0;\n\t\t\t.options {\n\t\t\t\t//background-color:#f6f9fc;\n\t\t\t\tborder-top: 1px solid rgba(0,0,0,0.1);\n\t\t\t\tpadding: 12px 18px;\n\t\t\t}\n\n\t\t\tb {\n\t\t\t\tmargin: 5px 0;\n\t\t\t\tdisplay: inline-block;\n\t\t\t}\n\t\t}\n\n\t\t.title {\n\t\t\tfont-size: 12px;\n\t\t\tline-height: normal;\n\t\t\tpadding: 13px 18px;\n\t\t\tdisplay: inline-block;\n\t\t}\n\t}\n\n\t&.tooltip-active:after,\n\t&:hover:after,\n\t&.tooltip-focus:focus:after,\n\t&.tooltip-active > .tooltip,\n\t&:hover > .tooltip,\n\t&.tooltip-focus:focus > .tooltip {\n\t\topacity: 1;\n\t\tbottom: calc(100% + 5px);\n\t}\n\n\t&.tooltip-delay:hover:after,\n\t&.tooltip-delay:hover > .tooltip,\n\t&.tooltip-delay.tooltip-focus:focus:after,\n\t&.tooltip-delay.tooltip-focus:focus > .tooltip {\n\t\ttransition-delay: 500ms;\n\t}\n\n\t&.tooltip-not-contained::after,\n\t&.tooltip-not-contained > .tooltip {\n\t\twhite-space: nowrap;\n\t\twidth: auto;\n\t}\n\n\t&.dark-tooltip {\n\t\t&:not(.tooltip-sub)::after,\n\t\t& > .tooltip {\n\t\t\tbackground-color: #111820;\n\t\t\tcolor: #FFF;\n\t\t}\n\t}\n\n\t&.bottom-tooltip {\n\t\t&::after,\n\t\t& > .tooltip {\n\t\t\tbottom: auto;\n\t\t\ttop: 90%;\n\t\t}\n\n\t\t&:hover::after,\n\t\t&:hover > .tooltip,\n\t\t&.tooltip-focus:focus::after,\n\t\t&.tooltip-focus:focus > .tooltip {\n\t\t\ttop: calc(100% + 5px);\n\t\t}\n\t}\n\n\t&.left-tooltip {\n\t\t&::after,\n\t\t& > .tooltip {\n\t\t\tright: 90%;\n\t\t\tleft: auto;\n\t\t\ttop: 50%;\n\t\t\ttransform: translateY(-50%);\n\t\t}\n\n\t\t&.bottom-tooltip {\n\t\t\t&::after,\n\t\t\t& > .tooltip {\n\t\t\t\ttop: 90%;\n\t\t\t\ttransform: none;\n\t\t\t}\n\t\t}\n\n\t\t&:hover::after,\n\t\t&:hover > .tooltip,\n\t\t&.tooltip-focus:focus::after,\n\t\t&.tooltip-focus:focus > .tooltip {\n\t\t\tright: calc(100% + 5px);\n\t\t}\n\t}\n\n\t&.right-tooltip {\n\t\t&::after,\n\t\t& > .tooltip {\n\t\t\tright: auto;\n\t\t\tleft: 90%;\n\t\t\ttop: 50%;\n\t\t\ttransform: translateY(-50%);\n\t\t}\n\n\t\t&:hover::after,\n\t\t&:hover > .tooltip,\n\t\t&.tooltip-focus:focus::after,\n\t\t&.tooltip-focus:focus > .tooltip {\n\t\t\tleft: calc(100% + 5px);\n\t\t}\n\t}\n}\n", "#wpbody-content {\n  padding-bottom: 45px;\n  box-sizing: border-box;\n  min-height: calc(100vh - 32px);\n}\n\n.loaded {\n  opacity: 1 !important;\n}\n\n.codes-pro-link {\n  margin-left: 5px;\n  display: inline-block;\n}\n\n#codes_editor_area {\n  margin-top: 10px;\n  border-radius: 4px;\n  /* Animations */\n}\n\n#codes_editor_area .switch-vertical {\n  width: 15px;\n  height: 25px;\n  border-radius: 30px;\n  position: relative;\n  cursor: pointer;\n}\n\n#codes_editor_area .switch-vertical .switch-fill {\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  background-color: rgba(255, 255, 255, 0.5);\n  border-radius: inherit;\n  transition: 300ms;\n}\n\n#codes_editor_area .switch-vertical > input {\n  position: absolute;\n  z-index: 1;\n  left: 50%;\n  transform: translateX(-50%);\n  top: 0;\n  appearance: none;\n  min-width: auto;\n  min-height: auto;\n  width: 15px;\n  height: 15px;\n  border-radius: 50%;\n  border: none;\n  display: block;\n  margin: 0;\n  transition: 300ms;\n  outline: none;\n  box-shadow: none;\n}\n\n#codes_editor_area .switch-vertical > input:checked {\n  top: 10px;\n}\n\n#codes_editor_area .switch-vertical > input:checked + .switch-fill {\n  background-color: #1ace1a;\n}\n\n#codes_editor_area .switch-vertical > input::before {\n  content: none;\n}\n\n#codes_editor_area .lang-notice {\n  background-color: #81848a;\n  color: #FFF;\n  padding: 11px 20px;\n  position: relative;\n}\n\n#codes_editor_area .lang-notice svg {\n  vertical-align: sub;\n  margin-right: 4px;\n}\n\n#codes_editor_area a {\n  text-decoration: none;\n  color: white;\n}\n\n#codes_editor_area a:hover, #codes_editor_area a:active {\n  color: #0073aa;\n}\n\n#codes_editor_area select {\n  line-height: 35px;\n  min-height: 35px;\n  box-shadow: none;\n}\n\n#codes_editor_area #topbar {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  height: 58px;\n  background-color: #131619;\n  border-radius: 4px 4px 0 0;\n  box-sizing: border-box;\n  padding: 0 10px;\n  border-bottom: 1px solid rgba(255, 255, 255, 0.15);\n  position: relative;\n  z-index: 1;\n}\n\n#codes_editor_area #topbar > * {\n  display: grid;\n  grid-auto-flow: column;\n  align-items: center;\n  gap: 15px;\n}\n\n#codes_editor_area #topbar img, #codes_editor_area #topbar svg {\n  display: block;\n}\n\n#codes_editor_area #topbar a:hover, #codes_editor_area #topbar a:focus {\n  outline: none;\n  box-shadow: none;\n}\n\n#codes_editor_area #topbar a:hover > svg path, #codes_editor_area #topbar a:focus > svg path {\n  fill: #0073aa;\n}\n\n#codes_editor_area #topbar select {\n  appearance: none;\n  background-image: url(data:image/svg+xml;charset=US-ASCII,%3Csvg%20width%3D%2220%22%20height%3D%2220%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3Cpath%20d%3D%22M5%206l5%205%205-5%202%201-7%207-7-7%202-1z%22%20fill%3D%22%23FFF%22%2F%3E%3C%2Fsvg%3E);\n  background-size: 14px 14px;\n  background-repeat: no-repeat;\n  background-position: 98% 50%;\n  background-color: #2F3235;\n  border: 1px solid #2F3235;\n  border-radius: 3px;\n  color: white;\n  font-size: 13px;\n  font-weight: 500;\n  padding: 0 30px 0 10px;\n  cursor: pointer;\n}\n\n#codes_editor_area #topbar select:hover, #codes_editor_area #topbar select:focus {\n  border-color: rgba(255, 255, 255, 0.5);\n}\n\n#codes_editor_area #topbar .indicators > .indicator {\n  color: white;\n  font-size: 32px;\n}\n\n#codes_editor_area #topbar .indicators > .indicator.unsaved {\n  color: orange;\n}\n\n#codes_editor_area #topbar .subtabs {\n  display: flex;\n}\n\n#codes_editor_area #topbar .subtabs .tooltip {\n  white-space: initial;\n  width: max-content;\n  max-width: 300px;\n}\n\n#codes_editor_area #topbar .subtabs button {\n  border: none;\n  background-color: #2F3235;\n  color: #939393;\n  height: 35px;\n  padding: 0 15px;\n  margin: 0;\n  cursor: pointer;\n  outline: none;\n  font-size: 12px;\n  font-weight: 500;\n  display: flex;\n  align-items: center;\n}\n\n#codes_editor_area #topbar .subtabs button:first-child {\n  border-top-left-radius: 2px;\n  border-bottom-left-radius: 2px;\n}\n\n#codes_editor_area #topbar .subtabs button:last-child {\n  border-top-right-radius: 2px;\n  border-bottom-right-radius: 2px;\n}\n\n#codes_editor_area #topbar .subtabs button:not(.saved) {\n  position: relative;\n}\n\n#codes_editor_area #topbar .subtabs button:not(.saved)::before {\n  content: \"\\2022\";\n  position: absolute;\n  right: 5px;\n  top: 10px;\n  font-size: 20px;\n  color: orange;\n  line-height: 10px;\n}\n\n#codes_editor_area #topbar .subtabs button:hover, #codes_editor_area #topbar .subtabs button:focus {\n  background-color: rgba(255, 255, 255, 0.15);\n}\n\n#codes_editor_area #topbar .subtabs button.active {\n  background-color: #fff;\n  color: #131619;\n}\n\n#codes_editor_area #topbar .subtabs button.active:focus {\n  box-shadow: 0 0 0 1px #007cba;\n}\n\n#codes_editor_area #topbar .subtabs button > span.dashicons {\n  margin-right: 3px;\n}\n\n#codes_editor_area #topbar .subtabs button > span.dashicons.l {\n  transform: rotate(-90deg);\n}\n\n#codes_editor_area #topbar .subtabs button > span.label.has-icon {\n  display: none;\n}\n\n@media (min-width: 1330px) {\n  #codes_editor_area #topbar .subtabs button > span.label.has-icon {\n    display: inline;\n  }\n}\n\n#codes_editor_area #topbar .subtabs button.disabled {\n  opacity: 0.3;\n}\n\n#codes_editor_area #topbar .subtabs button.hidden {\n  display: none;\n}\n\n#codes_editor_area .editors {\n  background-color: #131619;\n  position: relative;\n  z-index: 0;\n}\n\n#codes_editor_area .editors > .editor-addition {\n  position: absolute;\n  z-index: 5;\n  left: 0;\n  width: 100%;\n  height: 25px !important;\n  display: flex;\n}\n\n#codes_editor_area .editors > .editor-addition.before {\n  top: 0;\n  align-items: flex-start;\n}\n\n#codes_editor_area .editors > .editor-addition.after {\n  bottom: 0;\n  align-items: flex-end;\n}\n\n#codes_editor_area .editors > .editor-addition > span {\n  padding: 2px 5px;\n  opacity: 0.4;\n}\n\n#codes_editor_area .editors > .editor {\n  position: relative;\n  height: calc(100vh - 320px);\n}\n\n#codes_editor_area .editors > .editor:not([writable]) {\n  opacity: 0.7;\n}\n\n#codes_editor_area .editors > .editor:not([writable])::after {\n  content: \"This editor is not writable\";\n  color: white;\n  position: absolute;\n  bottom: 10px;\n  right: 10px;\n}\n\n#codes_editor_area .editors > .editor.output {\n  opacity: 0.8;\n}\n\n#codes_editor_area .editors > .editor textarea {\n  width: 100%;\n  border-radius: 0;\n}\n\n#codes_editor_area .editors .CodeMirror {\n  line-height: normal;\n  border-top-left-radius: 2px;\n  border-top-right-radius: 2px;\n  height: 100%;\n}\n\n#codes_editor_area .editors .CodeMirror-placeholder {\n  opacity: 0.2;\n}\n\n#codes_editor_area .editors .CodeMirror-lines > div {\n  padding-bottom: calc(100vh - 320px);\n}\n\n#codes_editor_area .editors .CodeMirror-sizer {\n  margin-top: 10px;\n}\n\n#codes_editor_area .editors .CodeMirror.cm-s-dark {\n  background-color: #131619;\n}\n\n#codes_editor_area .editors .CodeMirror.cm-s-dark .CodeMirror-gutters {\n  background-color: #131619;\n  border-right: 1px solid rgba(255, 255, 255, 0.15);\n}\n\n#codes_editor_area .editors.spacing .CodeMirror-sizer {\n  margin-top: 25px !important;\n  margin-bottom: 25px !important;\n}\n\n#codes_editor_area .editors.spacing.larger .editor-addition.before {\n  height: 35px !important;\n}\n\n#codes_editor_area .editors.spacing.larger .CodeMirror-sizer {\n  margin-top: 35px !important;\n  margin-bottom: 25px !important;\n}\n\n#codes_editor_area.loading .editors {\n  opacity: 0.5;\n}\n\n#codes_editor_area #bottombar {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  height: 36px;\n  background-color: #2F3235;\n  color: rgba(255, 255, 255, 0.6);\n  border-top: 1px solid rgba(255, 255, 255, 0.15);\n  border-radius: 0 0 4px 4px;\n  box-sizing: border-box;\n}\n\n#codes_editor_area #bottombar * {\n  color: rgba(255, 255, 255, 0.4);\n  font-size: 11px;\n  font-weight: 600;\n}\n\n#codes_editor_area #bottombar > * {\n  display: grid;\n  grid-auto-flow: column;\n  gap: 10px;\n  align-items: center;\n}\n\n#codes_editor_area #bottombar > *.left {\n  padding-left: 10px;\n}\n\n#codes_editor_area #bottombar > *.right {\n  justify-content: flex-end;\n}\n\n#codes_editor_area #bottombar > *.right span {\n  color: rgba(255, 255, 255, 0.6);\n  padding-right: 10px;\n}\n\n#codes_editor_area #bottombar label {\n  cursor: default;\n  display: flex;\n  align-items: center;\n}\n\n#codes_editor_area #bottombar select {\n  background-color: #2F3235;\n  border: none;\n  outline: none;\n  box-shadow: none;\n  border-radius: 0;\n  background-size: 10px 10px;\n}\n\n#codes_editor_area #bottombar select:hover {\n  cursor: pointer;\n  background-color: #131619;\n}\n\n#codes_editor_area #bottombar select:focus {\n  color: white;\n}\n\n#codes_editor_area #bottombar button {\n  background-color: #2F3235;\n  border: none;\n  border-left: 1px solid rgba(255, 255, 255, 0.15);\n  height: 35px;\n  padding: 0 20px;\n  color: white;\n  border-radius: 0;\n  border-bottom-right-radius: 2px;\n  cursor: pointer;\n  outline: none;\n}\n\n#codes_editor_area #bottombar button:hover, #codes_editor_area #bottombar button:focus {\n  background-color: rgba(255, 255, 255, 0.15);\n}\n\n#codes_editor_area #bottombar button.active {\n  background-color: #fff;\n  color: #2F3235;\n}\n\n#codes_editor_area #bottombar button.save {\n  background-color: #fff;\n  color: #2F3235;\n  margin-left: -9px;\n}\n\n#codes_editor_area #bottombar button.save::after {\n  right: 2px;\n  left: auto;\n  transform: none;\n}\n\n#codes_editor_area #bottombar button.save:hover, #codes_editor_area #bottombar button.save:focus {\n  opacity: 0.7;\n}\n\n#codes_editor_area #bottombar button.save[disabled] {\n  opacity: 0.3;\n}\n\n#codes_editor_area.fullscreen {\n  position: fixed;\n  z-index: 99999;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  width: 100%;\n  height: 100%;\n  margin: 0;\n  background-color: #36393b;\n}\n\n#codes_editor_area.fullscreen #topbar {\n  border-radius: 0;\n}\n\n#codes_editor_area.fullscreen .editors > .editor {\n  height: calc(100vh - 58px - 36px);\n}\n\n#codes_editor_area.fullscreen .editors > .editor .CodeMirror {\n  border-radius: 0;\n}\n\n#codes_editor_area.fullscreen #bottombar {\n  border-radius: 0;\n}\n\n#codes_editor_area .spin {\n  animation: spin 1s linear infinite;\n}\n\n@keyframes spin {\n  100% {\n    transform: rotate(360deg);\n  }\n}\n\n#codes_location > label {\n  display: flex;\n  align-items: center;\n  margin-top: 10px;\n}\n\n#codes_location > label input {\n  margin-bottom: -3px;\n}\n\n#codes_location > label input[disabled] {\n  cursor: auto;\n}\n\n#codes_location > label svg, #codes_location > label span.dashicons {\n  font-size: 20px;\n  width: 20px;\n  height: 20px;\n  margin-right: 3px;\n}\n\n#codes_location > label[disabled] {\n  opacity: 0.7;\n  cursor: auto;\n}\n\n#codes_location p {\n  margin-bottom: 0;\n}\n\n#codes_location hr {\n  margin-top: 15px;\n}\n\n.codes-free #codes_includes_box {\n  opacity: 0.7;\n}\n\n/* TOOLTIPS */\n[data-tooltip] {\n  position: relative;\n}\n\n[data-tooltip].tooltip {\n  cursor: help;\n}\n\n[data-tooltip]:not(.tooltip-sub)::after,\n[data-tooltip] > .tooltip {\n  content: attr(data-tooltip);\n  pointer-events: none;\n  position: absolute;\n  z-index: 9999999;\n  display: inline-table;\n  padding: 4px 6px;\n  width: 120px;\n  min-height: fit-content;\n  border-radius: 4px;\n  background-color: #FFF;\n  color: #111820;\n  box-shadow: 0px 2px 15px rgba(0, 0, 0, 0.5);\n  text-transform: initial;\n  font-family: -apple-system,BlinkMacSystemFont,\"Segoe UI\",Roboto,Oxygen-Sans,Ubuntu,Cantarell,\"Helvetica Neue\",sans-serif;\n  font-size: 12px;\n  line-height: 15px;\n  font-weight: 500;\n  letter-spacing: normal;\n  white-space: normal;\n  text-align: center;\n  opacity: 0;\n  transition: 500ms;\n  transition-delay: 0;\n  bottom: 90%;\n  right: auto;\n  left: 50%;\n  transform: translateX(-50%);\n}\n\n[data-tooltip] > .tooltip {\n  font-weight: normal;\n  text-align: left;\n  line-height: 20px;\n  overflow: hidden;\n  border-radius: 4px;\n}\n\n[data-tooltip] > .tooltip.shortcuts {\n  padding: 0;\n}\n\n[data-tooltip] > .tooltip.shortcuts .options {\n  border-top: 1px solid rgba(0, 0, 0, 0.1);\n  padding: 12px 18px;\n}\n\n[data-tooltip] > .tooltip.shortcuts b {\n  margin: 5px 0;\n  display: inline-block;\n}\n\n[data-tooltip] > .tooltip .title {\n  font-size: 12px;\n  line-height: normal;\n  padding: 13px 18px;\n  display: inline-block;\n}\n\n[data-tooltip].tooltip-active:after, [data-tooltip]:hover:after, [data-tooltip].tooltip-focus:focus:after,\n[data-tooltip].tooltip-active > .tooltip,\n[data-tooltip]:hover > .tooltip,\n[data-tooltip].tooltip-focus:focus > .tooltip {\n  opacity: 1;\n  bottom: calc(100% + 5px);\n}\n\n[data-tooltip].tooltip-delay:hover:after,\n[data-tooltip].tooltip-delay:hover > .tooltip, [data-tooltip].tooltip-delay.tooltip-focus:focus:after,\n[data-tooltip].tooltip-delay.tooltip-focus:focus > .tooltip {\n  transition-delay: 500ms;\n}\n\n[data-tooltip].tooltip-not-contained::after,\n[data-tooltip].tooltip-not-contained > .tooltip {\n  white-space: nowrap;\n  width: auto;\n}\n\n[data-tooltip].dark-tooltip:not(.tooltip-sub)::after,\n[data-tooltip].dark-tooltip > .tooltip {\n  background-color: #111820;\n  color: #FFF;\n}\n\n[data-tooltip].bottom-tooltip::after,\n[data-tooltip].bottom-tooltip > .tooltip {\n  bottom: auto;\n  top: 90%;\n}\n\n[data-tooltip].bottom-tooltip:hover::after,\n[data-tooltip].bottom-tooltip:hover > .tooltip, [data-tooltip].bottom-tooltip.tooltip-focus:focus::after,\n[data-tooltip].bottom-tooltip.tooltip-focus:focus > .tooltip {\n  top: calc(100% + 5px);\n}\n\n[data-tooltip].left-tooltip::after,\n[data-tooltip].left-tooltip > .tooltip {\n  right: 90%;\n  left: auto;\n  top: 50%;\n  transform: translateY(-50%);\n}\n\n[data-tooltip].left-tooltip.bottom-tooltip::after,\n[data-tooltip].left-tooltip.bottom-tooltip > .tooltip {\n  top: 90%;\n  transform: none;\n}\n\n[data-tooltip].left-tooltip:hover::after,\n[data-tooltip].left-tooltip:hover > .tooltip, [data-tooltip].left-tooltip.tooltip-focus:focus::after,\n[data-tooltip].left-tooltip.tooltip-focus:focus > .tooltip {\n  right: calc(100% + 5px);\n}\n\n[data-tooltip].right-tooltip::after,\n[data-tooltip].right-tooltip > .tooltip {\n  right: auto;\n  left: 90%;\n  top: 50%;\n  transform: translateY(-50%);\n}\n\n[data-tooltip].right-tooltip:hover::after,\n[data-tooltip].right-tooltip:hover > .tooltip, [data-tooltip].right-tooltip.tooltip-focus:focus::after,\n[data-tooltip].right-tooltip.tooltip-focus:focus > .tooltip {\n  left: calc(100% + 5px);\n}\n"]}