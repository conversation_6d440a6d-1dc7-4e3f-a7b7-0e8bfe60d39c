{"version": 3, "sources": ["settings.scss", "settings.css"], "names": [], "mappings": "AAGA;EACC,sBAAY;EACZ,iCAAgB;EAChB,2BAAuB;EACvB,4BAAuB;EACvB,wCAAgB;EAChB,kCAAwB;EACxB,2BAAiB;ACFlB;;ADMA;EACC,WAAW;EACX,SAAS;EACT,cAAc;EACd,qBAAqB;ACHtB;;ADDA;EAOE,gBAAgB;EAChB,gBAAgB;ACFlB;;ADNA;EAYE,0BAA0B;ACF5B;;ADVA;EAgBE,sBAAsB;EACtB,cAAc;EACd,4CAA4C;ACF9C;;ADhBA;EAqBG,mBAAmB;ACDtB;;ADpBA;EAyBG,SAAS;EACT,yBAAyB;ACD5B;;ADzBA;EAgCE,aAAa;EACb,qBAAqB;EACrB,4BAAuB;EACvB,2BAAsB;OAAtB,sBAAsB;EACtB,WAAW;EACX,2BAAsB;OAAtB,sBAAsB;EACtB,2DAA2D;EAC3D,+BAA+B;EAC/B,4CAAY;EACZ,2GAAwB;EACxB,8GAAiB;EAAjB,yGAAiB;EACjB,iGAA4F;OAA5F,4FAA4F;EAC5F,yDAAgB;EAChB,uBAAkB;EAClB,6BAAwB;EAAxB,wBAAwB;EACxB,6DAAwD;OAAxD,wDAAwD;EACxD,+BAA0B;OAA1B,0BAA0B;EAC1B,sBAAsB;EACtB,cAAc;ACHhB;;AD/CA;EAuDG,cAAc;ACJjB;;ADnDA;EA4DE,yBAzEwB;EA0ExB,WAAW;EACX,kBAAkB;EAClB,2BAA2B;ACL7B;;AD1DA;EAkEG,iBAAa;EAAb,aAAa;EACb,sBAAmB;OAAnB,mBAAmB;EACnB,sBAA8B;OAA9B,8BAA8B;ACJjC;;ADhEA;EAuEI,iBAAa;EAAb,aAAa;EACb,sBAAmB;OAAnB,mBAAmB;ACHvB;;ADrEA;EA4EI,eAAe;EACf,gBAAgB;ACHpB;;AD1EA;EAgFK,qBAAqB;EACrB,WAAW;EACX,eAAe;EACf,mBAAmB;EACnB,iBAAiB;EACjB,yBAAoB;OAApB,oBAAoB;EACpB,gBAAgB;EAChB,eAAe;ACFpB;;ADrFA;EA+FK,+BAA+B;EAC/B,eAAe;EACf,gBAAgB;EAChB,qBAAqB;EACrB,0CAA0C;EAC1C,iBAAa;EAAb,aAAa;EACb,YAAY;EACZ,sBAAmB;OAAnB,mBAAmB;EACnB,iBAAiB;EACjB,yBAAoB;OAApB,oBAAoB;EACpB,eAAe;EACf,iBAAiB;EACjB,yBAAoB;EAApB,oBAAoB;ACNzB;;ADrGA;EA8GM,0CAAyC;EACzC,WAAW;ACLjB;;AD1GA;EAmHM,WAAW;ACLjB;;AD9GA;EAuHM,qBAAqB;ACL3B;;ADlHA;EA2HM,iBAAiB;ACLvB;;ADtHA;EAsIE,uBAAuB;EACvB,gBAAgB;EAChB,eAAe;EAEf,oBAAoB;EACpB,eAAe;EACf,mBAAmB;EACnB,qBAAqB;EACrB,iCAAgB;EAChB,2BAAuB;EACvB,4BAAuB;EACvB,2BAAsB;OAAtB,sBAAsB;EACtB,gBAAU;OAAV,UAAU;EACV,kBAAgB;EAChB,2DAA2D;EAC3D,kBAAkB;EAClB,gBAAgB;EAChB,mBAAmB;EACnB,4CAAY;EACZ,2GAAwB;EACxB,8GAAiB;EAAjB,yGAAiB;EACjB,iGAE2B;OAF3B,4FAE2B;EAC3B,4DAAgB;EAChB,uBAAkB;EAClB,6BAAwB;EAAxB,wBAAwB;EACxB,6DAAwD;OAAxD,wDAAwD;EACxD,+BAA0B;OAA1B,0BAA0B;EAC1B,YAAY;EACZ,iBAAa;EAAb,aAAa;EACb,sBAAmB;OAAnB,mBAAmB;ACfrB;;ADtJA;EAwKG,eAAe;EACf,iBAAiB;EACjB,gBAAgB;EAEhB,cAA4B;EAC5B,qBAAqB;EACrB,eAAe;EACf,kBAAkB;EAClB,wBAAoB;EAApB,oBAAoB;EACpB,gBAAgB;ACfnB;;ADlKA;EAsLI,cAAc;EACd,aAAa;EACb,qBAAgB;OAAhB,gBAAgB;AChBpB;;ADxKA;EA4LI,cAAc;AChBlB", "file": "settings.css", "sourcesContent": ["// Settings Page Styles\n$background-color: #0a0d15;\n\n:root {\n\t--tw-shadow: 0 0 #0000;\n\t--tw-ring-inset: var(--tw-empty,);\n\t--tw-ring-offset-width: 0px;\n\t--tw-ring-offset-color: #fff;\n\t--tw-ring-color: rgba(59, 130, 246, 0.5);\n\t--tw-ring-offset-shadow: 0 0 #0000;\n\t--tw-ring-shadow: 0 0 #0000;\n}\n\n\n#custom-codes-settings {\n\twidth: auto;\n\tmargin: 0;\n\tdisplay: block;\n\tmargin: 20px 20px 0 0;\n\n\t#setting-error-settings_updated {\n\t\tmargin-top: 30px;\n\t\tmargin-bottom: 0;\n\t}\n\n\tform {\n\t\tpadding: 5px 5px 20px 10px;\n\t}\n\n\t.section-title {\n\t\tpadding: 25px 0 15px 0;\n\t\tdisplay: block;\n\t\tborder-bottom: 1px solid rgba(0, 0, 0, 0.07);\n\n\t\th3 {\n\t\t\tmargin: 7px 0 7px 0;\n\t\t}\n\n\t\tp {\n\t\t\tmargin: 0;\n\t\t\tcolor: rgba(0, 0, 0, 0.6);\n\t\t}\n\n\t}\n\n\t.tab-content {\n\t\tdisplay: none;\n\t\tborder-color: #9da4b3;\n\t\t--tw-ring-offset-color: #fff;\n\t\tbox-sizing: border-box;\n\t\twidth: 100%;\n\t\tborder-radius: 0.25rem;\n\t\tbackground-color: rgba(255, 255, 255, var(--tw-bg-opacity));\n\t\tpadding: 0 1.75rem 1rem 1.75rem;\n\t\t--tw-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);\n\t\t--tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\n\t\t--tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);\n\t\tbox-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\n\t\t--tw-ring-color: rgba(17, 24, 39, var(--tw-ring-opacity));\n\t\t--tw-ring-opacity: 0.05;\n\t\ttransition-property: all;\n\t\ttransition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n\t\ttransition-duration: 150ms;\n\t\tbackground-color: #FFF;\n\t\tmargin: 20px 0;\n\n\n\n\t\t&.active {\n\t\t\tdisplay: block;\n\t\t}\n\t}\n\n\t.codes-header {\n\t\tbackground-color: $background-color;\n\t\tcolor: #fff;\n\t\tpadding: 18px 30px;\n\t\tmargin: -20px -20px 0 -20px;\n\n\t\t& > .top {\n\t\t\tdisplay: flex;\n\t\t\talign-items: center;\n\t\t\tjustify-content: space-between;\n\n\t\t\t& > * {\n\t\t\t\tdisplay: flex;\n\t\t\t\talign-items: center;\n\t\t\t}\n\n\t\t\t.branding {\n\t\t\t\tfont-size: 21px;\n\t\t\t\tfont-weight: 600;\n\n\t\t\t\t.version {\n\t\t\t\t\ttext-decoration: none;\n\t\t\t\t\tcolor: #FFF;\n\t\t\t\t\tmargin-top: 0px;\n\t\t\t\t\tbackground: #c33030;\n\t\t\t\t\tmargin-left: 10px;\n\t\t\t\t\tborder-radius: 100px;\n\t\t\t\t\tpadding: 2px 8px;\n\t\t\t\t\tfont-size: 10px;\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t\t.navigation {\n\n\t\t\t\ta {\n\t\t\t\t\tcolor: rgba(255, 255, 255, 0.5);\n\t\t\t\t\tfont-size: 13px;\n\t\t\t\t\tfont-weight: 500;\n\t\t\t\t\ttext-decoration: none;\n\t\t\t\t\tbackground-color: rgba(255, 255, 255, 0.1);\n\t\t\t\t\tdisplay: flex;\n\t\t\t\t\theight: 37px;\n\t\t\t\t\talign-items: center;\n\t\t\t\t\tline-height: 37px;\n\t\t\t\t\tborder-radius: 100px;\n\t\t\t\t\tpadding: 0 18px;\n\t\t\t\t\tmargin: 0 0 0 8px;\n\t\t\t\t\ttransition: 0.3s all;\n\n\t\t\t\t\t&.active {\n\t\t\t\t\t\tbackground-color:rgba(255, 255, 255, 0.2);\n\t\t\t\t\t\tcolor: #FFF;\n\t\t\t\t\t}\n\n\t\t\t\t\t&:hover {\n\t\t\t\t\t\tcolor: #fff;\n\t\t\t\t\t}\n\n\t\t\t\t\tspan {\n\t\t\t\t\t\tdisplay: inline-block;\n\t\t\t\t\t}\n\n\t\t\t\t\tsvg {\n\t\t\t\t\t\tmargin-right: 5px;\n\t\t\t\t\t}\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t}\n\n\t}\n\t.settings-tabs {\n\t\tmargin: 0 -20px 0 -30px;\n\t\tbackground: #FFF;\n\t\tpadding: 0 30px;\n\n\t\tfont-family: inherit;\n\t\tborder-width: 0;\n\t\tborder-style: solid;\n\t\tborder-color: #9da4b3;\n\t\t--tw-ring-inset: var(--tw-empty,);\n\t\t--tw-ring-offset-width: 0px;\n\t\t--tw-ring-offset-color: #fff;\n\t\tbox-sizing: border-box;\n\t\tflex: none;\n\t\t--tw-bg-opacity: 1;\n\t\tbackground-color: rgba(255, 255, 255, var(--tw-bg-opacity));\n\t\tfont-size: .835rem;\n\t\tfont-weight: 500;\n\t\tline-height: 1.5rem;\n\t\t--tw-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);\n\t\t--tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\n\t\t--tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);\n\t\tbox-shadow: var(--tw-ring-offset-shadow),\n\t\tvar(--tw-ring-shadow),\n\t\tvar(--tw-shadow, 0 0 #0000);\n\t\t--tw-ring-color: rgba(177, 184, 199, var(--tw-ring-opacity));\n\t\t--tw-ring-opacity: 0.05;\n\t\ttransition-property: all;\n\t\ttransition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n\t\ttransition-duration: 150ms;\n\t\theight: 50px;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\n\t\t& > a {\n\t\t\tfont-size: 14px;\n\t\t\tline-height: 24px;\n\t\t\tfont-weight: 600;\n\n\t\t\tcolor: rgba(97, 104, 119, 1);\n\t\t\ttext-decoration: none;\n\t\t\tfont-size: 14px;\n\t\t\tpadding: 15px 12px;\n\t\t\tdisplay: inline-flex;\n\t\t\tfont-weight: 500;\n\n\t\t\t&:hover,\n\t\t\t&:focus,\n\t\t\t&.active {\n\t\t\t\tcolor: #010101;\n\t\t\t\toutline: none;\n\t\t\t\tbox-shadow: none;\n\t\t\t}\n\n\t\t\t&.active {\n\t\t\t\tcolor: #c33030;\n\t\t\t}\n\n\t\t}\n\t}\n\n}\n", ":root {\n  --tw-shadow: 0 0 #0000;\n  --tw-ring-inset: var(--tw-empty,);\n  --tw-ring-offset-width: 0px;\n  --tw-ring-offset-color: #fff;\n  --tw-ring-color: rgba(59, 130, 246, 0.5);\n  --tw-ring-offset-shadow: 0 0 #0000;\n  --tw-ring-shadow: 0 0 #0000;\n}\n\n#custom-codes-settings {\n  width: auto;\n  margin: 0;\n  display: block;\n  margin: 20px 20px 0 0;\n}\n\n#custom-codes-settings #setting-error-settings_updated {\n  margin-top: 30px;\n  margin-bottom: 0;\n}\n\n#custom-codes-settings form {\n  padding: 5px 5px 20px 10px;\n}\n\n#custom-codes-settings .section-title {\n  padding: 25px 0 15px 0;\n  display: block;\n  border-bottom: 1px solid rgba(0, 0, 0, 0.07);\n}\n\n#custom-codes-settings .section-title h3 {\n  margin: 7px 0 7px 0;\n}\n\n#custom-codes-settings .section-title p {\n  margin: 0;\n  color: rgba(0, 0, 0, 0.6);\n}\n\n#custom-codes-settings .tab-content {\n  display: none;\n  border-color: #9da4b3;\n  --tw-ring-offset-color: #fff;\n  box-sizing: border-box;\n  width: 100%;\n  border-radius: 0.25rem;\n  background-color: rgba(255, 255, 255, var(--tw-bg-opacity));\n  padding: 0 1.75rem 1rem 1.75rem;\n  --tw-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\n  --tw-ring-color: rgba(17, 24, 39, var(--tw-ring-opacity));\n  --tw-ring-opacity: 0.05;\n  transition-property: all;\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n  transition-duration: 150ms;\n  background-color: #FFF;\n  margin: 20px 0;\n}\n\n#custom-codes-settings .tab-content.active {\n  display: block;\n}\n\n#custom-codes-settings .codes-header {\n  background-color: #0a0d15;\n  color: #fff;\n  padding: 18px 30px;\n  margin: -20px -20px 0 -20px;\n}\n\n#custom-codes-settings .codes-header > .top {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n}\n\n#custom-codes-settings .codes-header > .top > * {\n  display: flex;\n  align-items: center;\n}\n\n#custom-codes-settings .codes-header > .top .branding {\n  font-size: 21px;\n  font-weight: 600;\n}\n\n#custom-codes-settings .codes-header > .top .branding .version {\n  text-decoration: none;\n  color: #FFF;\n  margin-top: 0px;\n  background: #c33030;\n  margin-left: 10px;\n  border-radius: 100px;\n  padding: 2px 8px;\n  font-size: 10px;\n}\n\n#custom-codes-settings .codes-header > .top .navigation a {\n  color: rgba(255, 255, 255, 0.5);\n  font-size: 13px;\n  font-weight: 500;\n  text-decoration: none;\n  background-color: rgba(255, 255, 255, 0.1);\n  display: flex;\n  height: 37px;\n  align-items: center;\n  line-height: 37px;\n  border-radius: 100px;\n  padding: 0 18px;\n  margin: 0 0 0 8px;\n  transition: 0.3s all;\n}\n\n#custom-codes-settings .codes-header > .top .navigation a.active {\n  background-color: rgba(255, 255, 255, 0.2);\n  color: #FFF;\n}\n\n#custom-codes-settings .codes-header > .top .navigation a:hover {\n  color: #fff;\n}\n\n#custom-codes-settings .codes-header > .top .navigation a span {\n  display: inline-block;\n}\n\n#custom-codes-settings .codes-header > .top .navigation a svg {\n  margin-right: 5px;\n}\n\n#custom-codes-settings .settings-tabs {\n  margin: 0 -20px 0 -30px;\n  background: #FFF;\n  padding: 0 30px;\n  font-family: inherit;\n  border-width: 0;\n  border-style: solid;\n  border-color: #9da4b3;\n  --tw-ring-inset: var(--tw-empty,);\n  --tw-ring-offset-width: 0px;\n  --tw-ring-offset-color: #fff;\n  box-sizing: border-box;\n  flex: none;\n  --tw-bg-opacity: 1;\n  background-color: rgba(255, 255, 255, var(--tw-bg-opacity));\n  font-size: .835rem;\n  font-weight: 500;\n  line-height: 1.5rem;\n  --tw-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\n  --tw-ring-color: rgba(177, 184, 199, var(--tw-ring-opacity));\n  --tw-ring-opacity: 0.05;\n  transition-property: all;\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n  transition-duration: 150ms;\n  height: 50px;\n  display: flex;\n  align-items: center;\n}\n\n#custom-codes-settings .settings-tabs > a {\n  font-size: 14px;\n  line-height: 24px;\n  font-weight: 600;\n  color: #616877;\n  text-decoration: none;\n  font-size: 14px;\n  padding: 15px 12px;\n  display: inline-flex;\n  font-weight: 500;\n}\n\n#custom-codes-settings .settings-tabs > a:hover, #custom-codes-settings .settings-tabs > a:focus, #custom-codes-settings .settings-tabs > a.active {\n  color: #010101;\n  outline: none;\n  box-shadow: none;\n}\n\n#custom-codes-settings .settings-tabs > a.active {\n  color: #c33030;\n}\n"]}