:root {
  --tw-shadow: 0 0 #0000;
  --tw-ring-inset: var(--tw-empty,);
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: rgba(59, 130, 246, 0.5);
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
}

#custom-codes-settings {
  width: auto;
  margin: 0;
  display: block;
  margin: 20px 20px 0 0;
}

#custom-codes-settings #setting-error-settings_updated {
  margin-top: 30px;
  margin-bottom: 0;
}

#custom-codes-settings form {
  padding: 5px 5px 20px 10px;
}

#custom-codes-settings .section-title {
  padding: 25px 0 15px 0;
  display: block;
  border-bottom: 1px solid rgba(0, 0, 0, 0.07);
}

#custom-codes-settings .section-title h3 {
  margin: 7px 0 7px 0;
}

#custom-codes-settings .section-title p {
  margin: 0;
  color: rgba(0, 0, 0, 0.6);
}

#custom-codes-settings .tab-content {
  display: none;
  border-color: #9da4b3;
  --tw-ring-offset-color: #fff;
  -moz-box-sizing: border-box;
       box-sizing: border-box;
  width: 100%;
  -moz-border-radius: 0.25rem;
       border-radius: 0.25rem;
  background-color: rgba(255, 255, 255, var(--tw-bg-opacity));
  padding: 0 1.75rem 1rem 1.75rem;
  --tw-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 -moz-calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  -moz-box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
       box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
  --tw-ring-color: rgba(17, 24, 39, var(--tw-ring-opacity));
  --tw-ring-opacity: 0.05;
  -moz-transition-property: all;
  transition-property: all;
  -moz-transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
       transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  -moz-transition-duration: 150ms;
       transition-duration: 150ms;
  background-color: #FFF;
  margin: 20px 0;
}

#custom-codes-settings .tab-content.active {
  display: block;
}

#custom-codes-settings .codes-header {
  background-color: #0a0d15;
  color: #fff;
  padding: 18px 30px;
  margin: -20px -20px 0 -20px;
}

#custom-codes-settings .codes-header > .top {
  display: -moz-box;
  display: flex;
  -moz-box-align: center;
       align-items: center;
  -moz-box-pack: justify;
       justify-content: space-between;
}

#custom-codes-settings .codes-header > .top > * {
  display: -moz-box;
  display: flex;
  -moz-box-align: center;
       align-items: center;
}

#custom-codes-settings .codes-header > .top .branding {
  font-size: 21px;
  font-weight: 600;
}

#custom-codes-settings .codes-header > .top .branding .version {
  text-decoration: none;
  color: #FFF;
  margin-top: 0px;
  background: #c33030;
  margin-left: 10px;
  -moz-border-radius: 100px;
       border-radius: 100px;
  padding: 2px 8px;
  font-size: 10px;
}

#custom-codes-settings .codes-header > .top .navigation a {
  color: rgba(255, 255, 255, 0.5);
  font-size: 13px;
  font-weight: 500;
  text-decoration: none;
  background-color: rgba(255, 255, 255, 0.1);
  display: -moz-box;
  display: flex;
  height: 37px;
  -moz-box-align: center;
       align-items: center;
  line-height: 37px;
  -moz-border-radius: 100px;
       border-radius: 100px;
  padding: 0 18px;
  margin: 0 0 0 8px;
  -moz-transition: 0.3s all;
  transition: 0.3s all;
}

#custom-codes-settings .codes-header > .top .navigation a.active {
  background-color: rgba(255, 255, 255, 0.2);
  color: #FFF;
}

#custom-codes-settings .codes-header > .top .navigation a:hover {
  color: #fff;
}

#custom-codes-settings .codes-header > .top .navigation a span {
  display: inline-block;
}

#custom-codes-settings .codes-header > .top .navigation a svg {
  margin-right: 5px;
}

#custom-codes-settings .settings-tabs {
  margin: 0 -20px 0 -30px;
  background: #FFF;
  padding: 0 30px;
  font-family: inherit;
  border-width: 0;
  border-style: solid;
  border-color: #9da4b3;
  --tw-ring-inset: var(--tw-empty,);
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  -moz-box-sizing: border-box;
       box-sizing: border-box;
  -moz-box-flex: 0;
       flex: none;
  --tw-bg-opacity: 1;
  background-color: rgba(255, 255, 255, var(--tw-bg-opacity));
  font-size: .835rem;
  font-weight: 500;
  line-height: 1.5rem;
  --tw-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 -moz-calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  -moz-box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
       box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
  --tw-ring-color: rgba(177, 184, 199, var(--tw-ring-opacity));
  --tw-ring-opacity: 0.05;
  -moz-transition-property: all;
  transition-property: all;
  -moz-transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
       transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  -moz-transition-duration: 150ms;
       transition-duration: 150ms;
  height: 50px;
  display: -moz-box;
  display: flex;
  -moz-box-align: center;
       align-items: center;
}

#custom-codes-settings .settings-tabs > a {
  font-size: 14px;
  line-height: 24px;
  font-weight: 600;
  color: #616877;
  text-decoration: none;
  font-size: 14px;
  padding: 15px 12px;
  display: -moz-inline-box;
  display: inline-flex;
  font-weight: 500;
}

#custom-codes-settings .settings-tabs > a:hover, #custom-codes-settings .settings-tabs > a:focus, #custom-codes-settings .settings-tabs > a.active {
  color: #010101;
  outline: none;
  -moz-box-shadow: none;
       box-shadow: none;
}

#custom-codes-settings .settings-tabs > a.active {
  color: #c33030;
}

/*# sourceMappingURL=settings.css.map */
