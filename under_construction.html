<!-- This is an under construction page provided by Panama Hosting to use with your sites as needed.  This file includes instructions on how to activate it as a landing page for your site, how to incorporate your personal logo, and how to change the text.  -->

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<meta author="PanamaHosting Hosting" />
<title>Our Site is Under Construction</title>
<link href='http://fonts.googleapis.com/css?family=Gentium+Book+Basic:400,400italic' rel='stylesheet' type='text/css'>
<style type="text/css">
body{
        background: #fcfaf2;
        font-family: <PERSON>lkorn;
        padding-top: 100px;
        text-align: center;
}
#heading{
        color: #353535;
        font: italic normal normal 72px 'Gentium Book Basic';
}
p{      
        border-bottom: 1px solid #e2e1d9;
        border-top: 1px solid #e2e1d9;
        color: #c90707;
        font: normal normal normal 18px 'Gentium Book Basic', serif;
        margin: 10px auto 0;
        padding: 20px 0;
        width: 750px
}
#logo{
        display: block;
        margin: 0 auto 10px;
}
</style>
</head>

<body>
	
<!--    If you would like to use your own logo, please follow these instructions:

        1.) Name your logo file "logo.jpg."  Ensure the extension is "jpg" in lower-case, rather than in upper-case "JPG," because the code that calls the file is case sensitive.  
        2.) Login to your cPanel account.  
        3.) Click on "File Manager" under the "Files" Section
        4.) You will get prompted with a pop-up menu asking what directory you would like to start in.  Select the public_html directory and click Go.
        5.) Click the Upload link at the top of the File Manager.
        6.) Browse your local computer for the file you want to upload. 
        7.) Select the file and click Open. 
        8.) Close the Upload files page. Refresh your File Manager by clicking the Reload link at the top middle of the File Manager.  You should see "logo.jpg" listed in the File Manager now.
        9.) If you downloaded this HTML file from the Support Center, follow steps 5 through 8 to upload this file as well.
        10.) In the File Manager, select the file under-construction.html and click the "Code Editor" icon on top of the page.  
        11.) This will open a dialog box with instructions for using the code editor.  Click "Edit" in the lower right corner of the box to open the under construction file for editing.
        12.) You will see the page is written in HTML and CSS.  Locate the line numbers on the left hand side of the page, then locate the following code on line number 61: <!--<img src="logo.jpg" id="logo"> -->
<!--    13.) The code on line 61 is for the logo and it is commented out, meaning it is not viewable to the public.  Remove the comment portions of the code by deleting the first four characters that are <!-- and the last three characters that are -->
<!--    14.) Click the "Save Changes" button in the top right.  
        15.) Click the "Close" button in the top right.  This will lead you back to the File Manager.
        16.) Select the file under-construction.html and click the "Rename" icon on the top of the page.  This will open the dialog box for renaming the HTML file.
        17.) Delete the text "under-construction.html" in the input box and type in "index.html".  Click the "Rename File" button on the bottom right.  This will activate the under construction page in your site.  You should see the logo appear at the top of the page, as well.  If you accidentally break the code and you're unsure of how to fix it, you can always download an original copy of this HTML file in our Support Center and reupload it to the public_html folder.  You can access the original copy here: You may also change the text that appears on the page by locating the text in the code below

-->
    
    <!--<img src="logo.jpg" id="logo"> -->
    
    <div id="heading">
        Under Construction <!-- Updating this line of code will change the larger text on the top -->
    </div>
    <p>
        We apologize for any inconvenience and should be back up with a newly updated website soon.  <!-- Updating this line of code will change the smaller  text -->
    </p>
</body>
    
</html>

<!-- This is an under construction page provided by Panamahosting Hosting to use with your sites as needed.  This file includes instructions on how to activate it as a landing page for your site, how to incorporate your personal logo, and how to change the text.  -->

