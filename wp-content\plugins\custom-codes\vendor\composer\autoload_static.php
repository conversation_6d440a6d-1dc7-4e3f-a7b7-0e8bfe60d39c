<?php

// autoload_static.php @generated by Composer

namespace Composer\Autoload;

class ComposerStaticInit26424e6d35aa9aadf36354a35af0cf77
{
    public static $files = array (
        '8d50dc88e56bace65e1e72f6017983ed' => __DIR__ . '/..' . '/freemius/wordpress-sdk/start.php',
    );

    public static $prefixLengthsPsr4 = array (
        'S' => 
        array (
            'ScssPhp\\ScssPhp\\' => 16,
        ),
    );

    public static $prefixDirsPsr4 = array (
        'ScssPhp\\ScssPhp\\' => 
        array (
            0 => __DIR__ . '/..' . '/scssphp/scssphp/src',
        ),
    );

    public static $classMap = array (
        'Composer\\InstalledVersions' => __DIR__ . '/..' . '/composer/InstalledVersions.php',
    );

    public static function getInitializer(ClassLoader $loader)
    {
        return \Closure::bind(function () use ($loader) {
            $loader->prefixLengthsPsr4 = ComposerStaticInit26424e6d35aa9aadf36354a35af0cf77::$prefixLengthsPsr4;
            $loader->prefixDirsPsr4 = ComposerStaticInit26424e6d35aa9aadf36354a35af0cf77::$prefixDirsPsr4;
            $loader->classMap = ComposerStaticInit26424e6d35aa9aadf36354a35af0cf77::$classMap;

        }, null, ClassLoader::class);
    }
}
