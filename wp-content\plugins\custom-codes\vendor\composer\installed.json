{"packages": [{"name": "freemius/wordpress-sdk", "version": "2.11.0", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/Freemius/wordpress-sdk.git", "reference": "9ad5477e4f0a39999b96bb21d0327bc6841d4590"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Freemius/wordpress-sdk/zipball/9ad5477e4f0a39999b96bb21d0327bc6841d4590", "reference": "9ad5477e4f0a39999b96bb21d0327bc6841d4590", "shasum": ""}, "require": {"php": ">=5.6"}, "require-dev": {"dealerdirect/phpcodesniffer-composer-installer": "^1.0", "phpcompatibility/php-compatibility": "^9.3", "phpcompatibility/phpcompatibility-wp": "^2.1", "phpstan/extension-installer": "^1.3", "squizlabs/php_codesniffer": "^3.7", "szepeviktor/phpstan-wordpress": "^1.3", "wp-coding-standards/wpcs": "^2.3"}, "time": "2025-02-09T07:06:24+00:00", "type": "library", "installation-source": "dist", "autoload": {"files": ["start.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-3.0-only"], "description": "Freemius WordPress SDK", "homepage": "https://freemius.com", "keywords": ["<PERSON><PERSON><PERSON>", "plugin", "sdk", "theme", "wordpress", "wordpress-plugin", "wordpress-theme"], "support": {"issues": "https://github.com/Freemius/wordpress-sdk/issues", "source": "https://github.com/Freemius/wordpress-sdk/tree/2.11.0"}, "install-path": "../freemius/wordpress-sdk"}, {"name": "scssphp/scssphp", "version": "v1.13.0", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/scssphp/scssphp.git", "reference": "63d1157457e5554edf00b0c1fabab4c1511d2520"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/scssphp/scssphp/zipball/63d1157457e5554edf00b0c1fabab4c1511d2520", "reference": "63d1157457e5554edf00b0c1fabab4c1511d2520", "shasum": ""}, "require": {"ext-ctype": "*", "ext-json": "*", "php": ">=5.6.0"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.4", "phpunit/phpunit": "^5.7 || ^6.5 || ^7.5 || ^8.3 || ^9.4", "sass/sass-spec": "*", "squizlabs/php_codesniffer": "~3.5", "symfony/phpunit-bridge": "^5.1", "thoughtbot/bourbon": "^7.0", "twbs/bootstrap": "~5.0", "twbs/bootstrap4": "4.6.1", "zurb/foundation": "~6.7.0"}, "suggest": {"ext-iconv": "Can be used as fallback when ext-mbstring is not available", "ext-mbstring": "For best performance, mbstring should be installed as it is faster than ext-iconv"}, "time": "2024-08-17T21:02:11+00:00", "bin": ["bin/pscss"], "type": "library", "extra": {"bamarni-bin": {"bin-links": false, "forward-command": false}}, "installation-source": "dist", "autoload": {"psr-4": {"ScssPhp\\ScssPhp\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/robocoder"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Cerdic"}], "description": "scssphp is a compiler for SCSS written in PHP.", "homepage": "http://scssphp.github.io/scssphp/", "keywords": ["css", "less", "sass", "scss", "stylesheet"], "support": {"issues": "https://github.com/scssphp/scssphp/issues", "source": "https://github.com/scssphp/scssphp/tree/v1.13.0"}, "install-path": "../scssphp/scssphp"}], "dev": false, "dev-package-names": []}